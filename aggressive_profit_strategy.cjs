const fs = require('fs');
const csv = require('csv-parser');
const { RSI, EMA, SMA, MACD, BollingerBands, ATR } = require('technicalindicators');

class AggressiveProfitStrategy {
  constructor(initialBalance = 40) {
    this.data = [];
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
    this.trades = [];
    this.totalWithdrawn = 0;
    this.maxBalance = initialBalance;
    this.maxDrawdown = 0;

    // EXTREME PROFIT-FOCUSED PARAMETERS
    this.TARGET_TOTAL = 25000;
    this.KEEP_BALANCE = 40;
    
    // ULTRA-AGGRESSIVE SETTINGS FOR MAXIMUM PROFIT
    this.currentLeverage = 125; // Extreme leverage
    this.currentPositionSize = 0.98; // 98% of balance
    this.currentStopLoss = 0.12; // 12% stop loss (wide for big moves)
    this.currentTakeProfit = 0.40; // 40% take profit (massive targets)
    
    // Market analysis results
    this.marketVolatility = 0;
    this.marketTrend = "NEUTRAL";
    this.priceRange = { min: 0, max: 0, range: 0 };
    this.bigMoveFrequency = 0;
    
    console.log("💥 AGGRESSIVE PROFIT STRATEGY - MAXIMUM RISK/REWARD");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`🎯 Target Total: ${this.TARGET_TOTAL} USDT (${(this.TARGET_TOTAL/initialBalance).toFixed(0)}x)`);
    console.log(`📊 Position Size: 98% (maximum exposure)`);
    console.log(`⚡ Leverage: 125x (extreme leverage)`);
    console.log(`🎯 Take Profit: 40% | Stop Loss: 12%`);
    console.log(`🔥 FOCUS: PROFIT AMOUNT > WIN RATE`);
  }

  async loadData(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on('end', () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          console.log(`⏰ Period: ${results[0]?.time} → ${results[results.length - 1]?.time}`);
          
          this.analyzeMarketForMaxProfit();
          resolve();
        })
        .on('error', reject);
    });
  }

  analyzeMarketForMaxProfit() {
    console.log("\n🔥 ANALYZING MARKET FOR MAXIMUM PROFIT OPPORTUNITIES...");
    
    const prices = this.data.map(d => d.close);
    const volumes = this.data.map(d => d.volume);
    
    // Calculate market characteristics
    this.priceRange.min = Math.min(...prices);
    this.priceRange.max = Math.max(...prices);
    this.priceRange.range = (this.priceRange.max / this.priceRange.min - 1) * 100;
    
    // Analyze volatility and big moves
    const priceChanges = [];
    const volatilities = [];
    let bigMoves = 0;
    let extremeMoves = 0;
    
    for (let i = 1; i < this.data.length; i++) {
      const prev = this.data[i - 1];
      const curr = this.data[i];
      
      const priceChange = (curr.close - prev.close) / prev.close;
      const volatility = (curr.high - curr.low) / curr.close;
      
      priceChanges.push(priceChange);
      volatilities.push(volatility);
      
      if (Math.abs(priceChange) > 0.02) bigMoves++;
      if (Math.abs(priceChange) > 0.05) extremeMoves++;
    }
    
    this.marketVolatility = volatilities.reduce((sum, vol) => sum + vol, 0) / volatilities.length;
    this.bigMoveFrequency = bigMoves / this.data.length;
    const maxMove = Math.max(...priceChanges.map(Math.abs));
    const totalReturn = (prices[prices.length - 1] - prices[0]) / prices[0];
    
    console.log(`📈 Price Range: ${this.priceRange.min.toFixed(0)} → ${this.priceRange.max.toFixed(0)} (${this.priceRange.range.toFixed(1)}% total range)`);
    console.log(`📊 Total Market Return: ${(totalReturn * 100).toFixed(1)}%`);
    console.log(`⚡ Average Volatility: ${(this.marketVolatility * 100).toFixed(2)}%`);
    console.log(`🚀 Maximum Single Move: ${(maxMove * 100).toFixed(2)}%`);
    console.log(`💥 Big Move Frequency: ${(this.bigMoveFrequency * 100).toFixed(2)}% of candles`);
    console.log(`🌋 Extreme Moves (>5%): ${extremeMoves}`);
    
    // Optimize parameters for this specific market
    this.optimizeForMarket(totalReturn, maxMove, this.bigMoveFrequency);
  }

  optimizeForMarket(totalReturn, maxMove, bigMoveFreq) {
    console.log("\n🧠 OPTIMIZING FOR MAXIMUM PROFIT...");
    
    // Adjust based on market characteristics
    if (this.priceRange.range > 200) {
      // Huge price range - ultra aggressive
      this.currentLeverage = 150;
      this.currentTakeProfit = 0.50; // 50% targets
      this.currentStopLoss = 0.15; // 15% stops
      console.log("🚀 HUGE PRICE RANGE DETECTED - Ultra-aggressive mode");
    } else if (this.priceRange.range > 100) {
      // Large price range - very aggressive
      this.currentLeverage = 125;
      this.currentTakeProfit = 0.40; // 40% targets
      this.currentStopLoss = 0.12; // 12% stops
      console.log("📈 LARGE PRICE RANGE DETECTED - Very aggressive mode");
    }
    
    // Adjust for volatility
    if (this.marketVolatility > 0.005) {
      this.currentTakeProfit *= 1.5; // Even higher targets in volatile markets
      console.log("⚡ HIGH VOLATILITY - Increasing profit targets");
    }
    
    // Adjust for big move frequency
    if (bigMoveFreq > 0.001) {
      this.currentLeverage = Math.min(200, this.currentLeverage * 1.2);
      console.log("💥 FREQUENT BIG MOVES - Increasing leverage");
    }
    
    console.log(`   → Final Leverage: ${this.currentLeverage}x`);
    console.log(`   → Final Position Size: ${(this.currentPositionSize * 100).toFixed(0)}%`);
    console.log(`   → Final Take Profit: ${(this.currentTakeProfit * 100).toFixed(0)}%`);
    console.log(`   → Final Stop Loss: ${(this.currentStopLoss * 100).toFixed(0)}%`);
    console.log(`   → Risk/Reward Ratio: 1:${(this.currentTakeProfit / this.currentStopLoss).toFixed(1)}`);
  }

  calculateIndicators(index) {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map(c => c.close);
    const highs = candles.map(c => c.high);
    const lows = candles.map(c => c.low);
    const volumes = candles.map(c => c.volume);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 8, values: closes }); // Faster for more signals
    const emaSlowValues = EMA.calculate({ period: 21, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });
    const macdValues = MACD.calculate({
      values: closes,
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
      SimpleMAOscillator: false,
      SimpleMASignal: false
    });
    const bbValues = BollingerBands.calculate({
      values: closes,
      period: 20,
      stdDev: 2
    });
    const atrValues = ATR.calculate({
      high: highs,
      low: lows,
      close: closes,
      period: 14
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
      macd: macdValues[macdValues.length - 1] || { MACD: 0, signal: 0, histogram: 0 },
      bb: bbValues[bbValues.length - 1] || { upper: closes[closes.length - 1], middle: closes[closes.length - 1], lower: closes[closes.length - 1] },
      atr: atrValues[atrValues.length - 1] || 0,
      volume: volumes[volumes.length - 1],
      avgVolume: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length,
      closes: closes
    };
  }

  shouldEnterLong(candle, indicators, prevCandle, index) {
    // AGGRESSIVE LONG ENTRY CONDITIONS - Focus on profit potential
    const currentPrice = candle.close;
    const volumeRatio = indicators.volume / indicators.avgVolume;
    const momentum = (currentPrice - prevCandle.close) / prevCandle.close;
    const atrPercent = indicators.atr / currentPrice;
    
    // Multiple aggressive entry scenarios
    const scenarios = [];
    
    // 1. Momentum Breakout (High profit potential)
    if (indicators.emaFast > indicators.emaSlow && 
        momentum > 0.005 && 
        volumeRatio > 1.2 && 
        candle.close > candle.open) {
      scenarios.push({ reason: "MOMENTUM_BREAKOUT", confidence: 0.7 });
    }
    
    // 2. Oversold Bounce (High profit potential)
    if (indicators.rsi < 35 && 
        indicators.emaFast > indicators.emaSlow && 
        volumeRatio > 1.0) {
      scenarios.push({ reason: "OVERSOLD_BOUNCE", confidence: 0.6 });
    }
    
    // 3. Bollinger Band Breakout (Explosive potential)
    if (currentPrice > indicators.bb.upper && 
        volumeRatio > 1.5 && 
        momentum > 0.003) {
      scenarios.push({ reason: "BB_BREAKOUT", confidence: 0.8 });
    }
    
    // 4. MACD Bullish Crossover
    if (indicators.macd.MACD > indicators.macd.signal && 
        indicators.macd.histogram > 0 && 
        indicators.emaFast > indicators.emaSlow) {
      scenarios.push({ reason: "MACD_BULLISH", confidence: 0.5 });
    }
    
    // 5. High Volatility Opportunity
    if (atrPercent > this.marketVolatility * 2 && 
        momentum > 0.002 && 
        volumeRatio > 1.1) {
      scenarios.push({ reason: "HIGH_VOLATILITY", confidence: 0.6 });
    }
    
    // 6. Trend Continuation
    if (indicators.emaFast > indicators.emaSlow && 
        indicators.emaSlow > indicators.sma && 
        currentPrice > indicators.sma && 
        momentum > 0.001) {
      scenarios.push({ reason: "TREND_CONTINUATION", confidence: 0.4 });
    }
    
    // Return the best scenario
    if (scenarios.length > 0) {
      const bestScenario = scenarios.reduce((best, current) => 
        current.confidence > best.confidence ? current : best
      );
      return { enter: true, reason: bestScenario.reason, confidence: bestScenario.confidence };
    }
    
    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldEnterShort(candle, indicators, prevCandle, index) {
    // AGGRESSIVE SHORT ENTRY CONDITIONS - Focus on profit potential
    const currentPrice = candle.close;
    const volumeRatio = indicators.volume / indicators.avgVolume;
    const momentum = (currentPrice - prevCandle.close) / prevCandle.close;
    const atrPercent = indicators.atr / currentPrice;
    
    // Multiple aggressive entry scenarios
    const scenarios = [];
    
    // 1. Momentum Breakdown (High profit potential)
    if (indicators.emaFast < indicators.emaSlow && 
        momentum < -0.005 && 
        volumeRatio > 1.2 && 
        candle.close < candle.open) {
      scenarios.push({ reason: "MOMENTUM_BREAKDOWN", confidence: 0.7 });
    }
    
    // 2. Overbought Rejection (High profit potential)
    if (indicators.rsi > 65 && 
        indicators.emaFast < indicators.emaSlow && 
        volumeRatio > 1.0) {
      scenarios.push({ reason: "OVERBOUGHT_REJECTION", confidence: 0.6 });
    }
    
    // 3. Bollinger Band Breakdown (Explosive potential)
    if (currentPrice < indicators.bb.lower && 
        volumeRatio > 1.5 && 
        momentum < -0.003) {
      scenarios.push({ reason: "BB_BREAKDOWN", confidence: 0.8 });
    }
    
    // 4. MACD Bearish Crossover
    if (indicators.macd.MACD < indicators.macd.signal && 
        indicators.macd.histogram < 0 && 
        indicators.emaFast < indicators.emaSlow) {
      scenarios.push({ reason: "MACD_BEARISH", confidence: 0.5 });
    }
    
    // 5. High Volatility Opportunity
    if (atrPercent > this.marketVolatility * 2 && 
        momentum < -0.002 && 
        volumeRatio > 1.1) {
      scenarios.push({ reason: "HIGH_VOLATILITY", confidence: 0.6 });
    }
    
    // 6. Trend Continuation
    if (indicators.emaFast < indicators.emaSlow && 
        indicators.emaSlow < indicators.sma && 
        currentPrice < indicators.sma && 
        momentum < -0.001) {
      scenarios.push({ reason: "TREND_CONTINUATION", confidence: 0.4 });
    }
    
    // Return the best scenario
    if (scenarios.length > 0) {
      const bestScenario = scenarios.reduce((best, current) => 
        current.confidence > best.confidence ? current : best
      );
      return { enter: true, reason: bestScenario.reason, confidence: bestScenario.confidence };
    }
    
    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldExit(candle, indicators) {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;

    if (this.position.side === "LONG") {
      if (currentPrice <= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice >= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };

      // Additional profit-maximizing exits
      if (indicators.rsi > 85) return { exit: true, reason: "EXTREME_OVERBOUGHT" };

      // Aggressive trailing stop for massive profits
      const profitPercent = (currentPrice - this.position.entryPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.3) {
        const trailingStop = this.position.entryPrice * (1 + profitPercent * 0.9);
        if (currentPrice < trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }

    } else { // SHORT
      if (currentPrice >= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice <= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };

      // Additional profit-maximizing exits
      if (indicators.rsi < 15) return { exit: true, reason: "EXTREME_OVERSOLD" };

      // Aggressive trailing stop for massive profits
      const profitPercent = (this.position.entryPrice - currentPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.3) {
        const trailingStop = this.position.entryPrice * (1 - profitPercent * 0.9);
        if (currentPrice > trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }
    }

    return { exit: false, reason: "HOLD" };
  }

  openPosition(side, candle, reason, confidence) {
    const positionSize = this.balance * this.currentPositionSize;
    const leverage = this.currentLeverage;
    const contractSize = (positionSize * leverage) / candle.close;

    // Dynamic adjustments based on confidence
    let stopLossPercent = this.currentStopLoss;
    let takeProfitPercent = this.currentTakeProfit;

    if (confidence > 0.7) {
      takeProfitPercent *= 1.3; // Even higher targets for high confidence
      stopLossPercent *= 1.2; // Wider stops for big moves
    }

    let stopLoss, takeProfit;

    if (side === "LONG") {
      stopLoss = candle.close * (1 - stopLossPercent);
      takeProfit = candle.close * (1 + takeProfitPercent);
    } else {
      stopLoss = candle.close * (1 + stopLossPercent);
      takeProfit = candle.close * (1 - takeProfitPercent);
    }

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
      stopLoss,
      takeProfit
    };

    console.log(`🚀 OPEN ${side} at ${candle.close.toFixed(2)} | Size: ${positionSize.toFixed(2)} USDT (${leverage}x)`);
    console.log(`   Confidence: ${(confidence * 100).toFixed(1)}% | Reason: ${reason}`);
    console.log(`   SL: ${stopLoss.toFixed(2)} (-${(stopLossPercent * 100).toFixed(1)}%) | TP: ${takeProfit.toFixed(2)} (+${(takeProfitPercent * 100).toFixed(1)}%)`);
    console.log(`   Potential Profit: ${(positionSize * leverage * takeProfitPercent).toFixed(2)} USDT`);
    console.log(`   Balance: ${this.balance.toFixed(2)} USDT`);
  }

  closePosition(candle, reason) {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent = (pnl / (this.balance * this.currentPositionSize)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance
    };

    this.trades.push(trade);

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    const totalValue = this.balance + this.totalWithdrawn;
    const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);

    console.log(`${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(2)} USDT (${pnlPercent.toFixed(2)}%)`);
    console.log(`   Entry: ${entryPrice.toFixed(2)} → Exit: ${exitPrice.toFixed(2)} | ${reason}`);
    console.log(`   Balance: ${this.balance.toFixed(2)} | Total Value: ${totalValue.toFixed(2)} | Progress: ${progress}%`);

    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
  }

  async runBacktest() {
    console.log(`\n🚀 Starting Aggressive Profit Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`⏰ Period: ${this.data[0]?.time} → ${this.data[this.data.length - 1]?.time}`);
    console.log(`🎯 Target: ${this.TARGET_TOTAL} USDT (${(this.TARGET_TOTAL / this.initialBalance).toFixed(0)}x return)`);

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      // Exit logic
      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      // Entry logic (only if no position and sufficient balance)
      if (!this.position.side && this.balance >= 1) {
        const longSignal = this.shouldEnterLong(candle, indicators, prevCandle, i);
        const shortSignal = this.shouldEnterShort(candle, indicators, prevCandle, i);

        // Enter trades aggressively - lower confidence threshold
        if (longSignal.enter && longSignal.confidence >= 0.3) {
          this.openPosition("LONG", candle, longSignal.reason, longSignal.confidence);
        } else if (shortSignal.enter && shortSignal.confidence >= 0.3) {
          this.openPosition("SHORT", candle, shortSignal.reason, shortSignal.confidence);
        }
      }

      // Progress updates every 3000 candles
      if (i % 3000 === 0) {
        const totalValue = this.balance + this.totalWithdrawn;
        const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);
        console.log(`📊 Progress Update: ${totalValue.toFixed(2)} USDT (${progress}%) | Trades: ${this.trades.length}`);
      }

      // Early exit if target achieved
      const currentTotalValue = this.balance + this.totalWithdrawn;
      if (currentTotalValue >= this.TARGET_TOTAL) {
        console.log(`🎉 TARGET ACHIEVED! Stopping backtest early.`);
        break;
      }

      // Emergency stop if balance too low
      if (this.balance < 0.1) {
        console.log(`💥 BALANCE TOO LOW! Stopping backtest.`);
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    this.printResults();
  }

  printResults() {
    const finalBalance = this.balance;
    const totalValue = finalBalance + this.totalWithdrawn;
    const totalReturn = totalValue - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = totalValue / this.initialBalance;

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl <= 0);
    const winRate = this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0;
    const avgWin = winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : avgWin > 0 ? 10 : 0;
    const maxWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0;
    const maxLoss = losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0;

    console.log("\n" + "=".repeat(80));
    console.log("💥 AGGRESSIVE PROFIT STRATEGY - FINAL RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${totalValue.toFixed(2)} USDT`);
    console.log(`📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(2)}%)`);
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(2)}% (${this.TARGET_TOTAL.toFixed(0)} USDT target)`);
    console.log(`🏆 Target Achieved: ${totalValue >= this.TARGET_TOTAL ? '✅ YES!' : '❌ NO'}`);
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}% (NOT THE FOCUS)`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);
    console.log(`🎯 Average Win: ${avgWin.toFixed(2)} USDT`);
    console.log(`💥 Average Loss: ${avgLoss.toFixed(2)} USDT`);
    console.log(`🚀 LARGEST WIN: ${maxWin.toFixed(2)} USDT`);
    console.log(`💀 LARGEST LOSS: ${maxLoss.toFixed(2)} USDT`);

    if (totalValue >= this.TARGET_TOTAL) {
      console.log("\n🎉 CONGRATULATIONS! 25,000 USDT TARGET ACHIEVED!");
      console.log(`🚀 Your ${this.initialBalance} USDT became ${totalValue.toFixed(2)} USDT!`);
      console.log(`💰 That's a ${returnMultiplier.toFixed(0)}x return!`);
    } else {
      console.log(`\n📈 Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(1)}% towards 25K target`);
      console.log(`💡 Strategy shows ${returnMultiplier.toFixed(2)}x return potential`);
      console.log(`🎯 Need ${(this.TARGET_TOTAL - totalValue).toFixed(2)} more USDT to reach target`);
    }

    // Save results
    const result = {
      strategy: "Aggressive Profit Strategy - Maximum Profit Focus",
      initialBalance: this.initialBalance,
      finalBalance,
      totalValue,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      maxWin,
      maxLoss,
      profitFactor,
      targetAchieved: totalValue >= this.TARGET_TOTAL,
      targetProgress: (totalValue / this.TARGET_TOTAL) * 100,
      trades: this.trades,
      marketAnalysis: {
        priceRange: this.priceRange,
        marketVolatility: this.marketVolatility,
        bigMoveFrequency: this.bigMoveFrequency
      },
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };

    const resultsFile = `./results/backtests/aggressive_profit_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  }
}

// Run the strategy
async function main() {
  const strategy = new AggressiveProfitStrategy(40);

  try {
    const dataFile = './data/history__15m_2024-01-31T17:00:00.000Z_2022-11-30T17:00:00.000Z.csv';
    console.log(`🚀 Running Aggressive Profit Strategy on: ${dataFile}`);

    await strategy.loadData(dataFile);
    await strategy.runBacktest();
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
